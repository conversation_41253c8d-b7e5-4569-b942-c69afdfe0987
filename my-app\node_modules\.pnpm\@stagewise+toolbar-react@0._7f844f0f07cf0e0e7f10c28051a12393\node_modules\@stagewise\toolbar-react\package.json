{"name": "@stagewise/toolbar-react", "private": false, "version": "0.6.2", "type": "module", "files": ["dist"], "license": "AGPL-3.0-only", "publishConfig": {"access": "public"}, "types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.umd.cjs", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.umd.cjs"}}, "dependencies": {"@stagewise/toolbar": "0.6.2"}, "devDependencies": {"@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@vitejs/plugin-react-swc": "^3.9.0", "globals": "^16.3.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4"}, "peerDependencies": {"@types/react": ">=18.0.0", "react": ">=18.0.0"}, "scripts": {"clean": "rm -rf .turbo dist node_modules", "dev": "tsc -b && vite build --mode development", "build": "tsc -b && vite build", "build:toolbar": "tsc -b && vite build"}}