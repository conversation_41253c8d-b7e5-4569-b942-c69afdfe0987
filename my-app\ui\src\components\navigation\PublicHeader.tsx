import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MobileNav } from './MobileNav';
import { ModeToggle } from '@/components/mode-toggle';
import { Search, Bell, Grid3X3, Diamond } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

// LocalBiz Logo Component with Diamond Icon
function LocalBizLogo({ className }: { className?: string }) {
  return (
    <div className={`flex items-center gap-2 ${className || ''}`}>
      <Diamond className="h-5 w-5 text-primary" />
      <span className="text-lg font-bold text-foreground">LocalBiz</span>
    </div>
  );
}

export function PublicHeader() {
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      const params = new URLSearchParams();
      params.set('q', searchQuery);
      navigate(`/search?${params.toString()}`);
    }
  };

  return (
    <header className="w-full bg-background border-b border-border">
      <div className="container mx-auto flex items-center justify-between px-6 py-4">
        {/* Logo Section */}
        <Link to="/" className="flex items-center">
          <LocalBizLogo />
        </Link>

        {/* Navigation Section - Center */}
        <nav className="hidden md:flex items-center gap-8">
          <Link
            to="/"
            className="text-sm font-medium text-foreground hover:text-primary transition-colors"
          >
            Home
          </Link>
          <Link
            to="/categories"
            className="text-sm font-medium text-foreground hover:text-primary transition-colors"
          >
            Categories
          </Link>
          <Link
            to="/businesses"
            className="text-sm font-medium text-foreground hover:text-primary transition-colors"
          >
            Deals
          </Link>
          <Link
            to="/contact"
            className="text-sm font-medium text-foreground hover:text-primary transition-colors"
          >
            Contact
          </Link>
        </nav>

        {/* Actions Section - Right */}
        <div className="flex items-center gap-4">
          {/* Desktop Search Bar */}
          <form onSubmit={handleSearch} className="hidden md:flex">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-80 h-8 text-sm bg-muted/30 border-border/50 focus:bg-background rounded-full"
              />
            </div>
          </form>

          {/* Notification Bell */}
          <Button variant="ghost" size="icon" className="hidden md:flex relative">
            <Bell className="h-5 w-5" />
            <span className="sr-only">Notifications</span>
            {/* Notification badge */}
            <span className="absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full"></span>
          </Button>

          {/* User Avatar */}
          <div className="hidden md:block">
            <div
              className="w-8 h-8 rounded-full bg-cover bg-center border border-border cursor-pointer hover:border-primary transition-colors"
              style={{
                backgroundImage: `url("https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face")`
              }}
            />
          </div>

          {/* Mobile Actions */}
          <div className="md:hidden flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <Link to="/search">
                <Search className="h-5 w-5" />
                <span className="sr-only">Search</span>
              </Link>
            </Button>
            <Button variant="ghost" size="icon">
              <Bell className="h-5 w-5" />
              <span className="sr-only">Notifications</span>
            </Button>
            <ModeToggle />
          </div>

          {/* Mobile Menu */}
          <MobileNav />
        </div>
      </div>
    </header>
  );
}
