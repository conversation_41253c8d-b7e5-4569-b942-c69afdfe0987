import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MobileNav } from './MobileNav';
import { ModeToggle } from '@/components/mode-toggle';
import { Search, Grid3X3 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

// LocalBiz Logo SVG Component
function LocalBizLogo({ className }: { className?: string }) {
  return (
    <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.475 21.6262C40.358 21.4363 40.6863 21.5589 40.7581 21.5934C40.7876 21.655 40.8547 21.857 40.8082 22.3336C40.7408 23.0255 40.4502 24.0046 39.8572 25.2301C38.6799 27.6631 36.5085 30.6631 33.5858 33.5858C30.6631 36.5085 27.6632 38.6799 25.2301 39.8572C24.0046 40.4502 23.0255 40.7407 22.3336 40.8082C21.8571 40.8547 21.6551 40.7875 21.5934 40.7581C21.5589 40.6863 21.4363 40.358 21.6262 39.475C21.8562 38.4054 22.4689 36.9657 23.5038 35.2817C24.7575 33.2417 26.5497 30.9744 28.7621 28.762C30.9744 26.5497 33.2417 24.7574 35.2817 23.5037C36.9657 22.4689 38.4054 21.8562 39.475 21.6262ZM4.41189 29.2403L18.7597 43.5881C19.8813 44.7097 21.4027 44.9179 22.7217 44.7893C24.0585 44.659 25.5148 44.1631 26.9723 43.4579C29.9052 42.0387 33.2618 39.5667 36.4142 36.4142C39.5667 33.2618 42.0387 29.9052 43.4579 26.9723C44.1631 25.5148 44.659 24.0585 44.7893 22.7217C44.9179 21.4027 44.7097 19.8813 43.5881 18.7597L29.2403 4.41187C27.8527 3.02428 25.8765 3.02573 24.2861 3.36776C22.6081 3.72863 20.7334 4.58419 18.8396 5.74801C16.4978 7.18716 13.9881 9.18353 11.5858 11.5858C9.18354 13.988 7.18717 16.4978 5.74802 18.8396C4.58421 20.7334 3.72865 22.6081 3.36778 24.2861C3.02574 25.8765 3.02429 27.8527 4.41189 29.2403Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function PublicHeader() {
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      const params = new URLSearchParams();
      params.set('q', searchQuery);
      navigate(`/search?${params.toString()}`);
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full bg-background/95 backdrop-blur-lg border-b border-border/20">
      <div className="flex items-center justify-between px-10 py-3">
        {/* Logo and Navigation */}
        <div className="flex items-center gap-8">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-3 text-foreground">
            <LocalBizLogo className="h-5 w-5" />
            <span className="text-lg font-bold leading-tight tracking-[-0.015em]">LocalBiz</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-8">
            <Link
              to="/"
              className="text-foreground text-sm font-medium leading-normal hover:text-primary transition-colors"
            >
              Home
            </Link>
            <Link
              to="/categories"
              className="text-foreground text-sm font-medium leading-normal hover:text-primary transition-colors"
            >
              Categories
            </Link>
            <Link
              to="/businesses"
              className="text-foreground text-sm font-medium leading-normal hover:text-primary transition-colors"
            >
              Deals
            </Link>
            <Link
              to="/about"
              className="text-foreground text-sm font-medium leading-normal hover:text-primary transition-colors"
            >
              About
            </Link>
          </nav>
        </div>

        {/* Search and Actions */}
        <div className="flex items-center gap-3">
          {/* Desktop Search Bar */}
          <form onSubmit={handleSearch} className="hidden md:flex">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-48"
              />
            </div>
          </form>

          {/* List Your Business Button */}
          <Button
            size="sm"
            className="hidden md:flex text-xs font-medium"
            asChild
          >
            <Link to="/apply">
              List Business
            </Link>
          </Button>

          {/* User Avatar */}
          <div className="hidden md:block">
            <div
              className="w-10 h-10 rounded-full bg-cover bg-center border-2 border-border"
              style={{
                backgroundImage: `url("https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face")`
              }}
            />
          </div>

          {/* Mobile Actions */}
          <div className="md:hidden flex items-center gap-2">
            <Button variant="ghost" size="icon" asChild>
              <Link to="/search">
                <Search className="h-5 w-5" />
                <span className="sr-only">Search</span>
              </Link>
            </Button>
            <Button variant="ghost" size="icon" asChild>
              <Link to="/categories">
                <Grid3X3 className="h-5 w-5" />
                <span className="sr-only">Categories</span>
              </Link>
            </Button>
            <ModeToggle />
          </div>

          {/* Mobile Menu */}
          <MobileNav />
        </div>
      </div>
    </header>
  );
}
