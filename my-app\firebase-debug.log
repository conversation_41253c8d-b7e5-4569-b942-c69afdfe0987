[debug] [2025-08-03T01:40:01.286Z] ----------------------------------------------------------------------
[debug] [2025-08-03T01:40:01.293Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\shop-bze\my-app\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only auth --project demo-project --export-on-exit=./data/firebase-emulator --import=./data/firebase-emulator
[debug] [2025-08-03T01:40:01.295Z] CLI Version:   13.35.1
[debug] [2025-08-03T01:40:01.295Z] Platform:      win32
[debug] [2025-08-03T01:40:01.295Z] Node Version:  v24.4.1
[debug] [2025-08-03T01:40:01.296Z] Time:          Sat Aug 02 2025 21:40:01 GMT-0400 (Eastern Daylight Time)
[debug] [2025-08-03T01:40:01.296Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-08-03T01:40:01.308Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[debug] [2025-08-03T01:40:02.583Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] Failed to authenticate, have you run firebase login?
[warn] !  emulators: You are not currently authenticated so some features may not work correctly. Please run firebase login to authenticate the CLI. 
[info] i  emulators: Starting emulators: auth {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth"}}
[info] i  emulators: Detected demo project ID "demo-project", emulated services will use a demo configuration and attempts to access non-emulated services for this project will fail. {"metadata":{"emulator":{"name":"hub"},"message":"Detected demo project ID \"demo-project\", emulated services will use a demo configuration and attempts to access non-emulated services for this project will fail."}}
[debug] [2025-08-03T01:40:02.800Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-03T01:40:02.800Z] [auth] Authentication Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-03T01:40:02.801Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":5504},{"address":"::1","family":"IPv6","port":5504}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":5503}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-08-03T01:40:02.818Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-demo-project.json
[info] i  auth: Importing config from C:\Users\<USER>\shop-bze\my-app\data\firebase-emulator\auth_export\config.json {"metadata":{"emulator":{"name":"auth"},"message":"Importing config from C:\\Users\\<USER>\\shop-bze\\my-app\\data\\firebase-emulator\\auth_export\\config.json"}}
[debug] [2025-08-03T01:40:34.494Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-08-03T01:40:34.494Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":*************,"cloudBuildWarnAfter":*************,"defaultNode10After":*************,"minVersion":"3.0.5","node8DeploysDisabledAfter":*************,"node8RuntimeDisabledAfter":*************,"node8WarnAfter":*************}
[info] i  auth: Importing accounts from C:\Users\<USER>\shop-bze\my-app\data\firebase-emulator\auth_export\accounts.json {"metadata":{"emulator":{"name":"auth"},"message":"Importing accounts from C:\\Users\\<USER>\\shop-bze\\my-app\\data\\firebase-emulator\\auth_export\\accounts.json"}}
[debug] [2025-08-03T01:40:34.615Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:5504/               │
└─────────────────────────────────────────────────────────────┘

┌────────────────┬────────────────┬────────────────────────────┐
│ Emulator       │ Host:Port      │ View in Emulator UI        │
├────────────────┼────────────────┼────────────────────────────┤
│ Authentication │ 127.0.0.1:5503 │ http://127.0.0.1:5504/auth │
└────────────────┴────────────────┴────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4400
  Other reserved ports: 4500

Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
