(function(global, factory) {
  typeof exports === "object" && typeof module !== "undefined" ? factory(exports, require("@stagewise/toolbar"), require("react")) : typeof define === "function" && define.amd ? define(["exports", "@stagewise/toolbar", "react"], factory) : (global = typeof globalThis !== "undefined" ? globalThis : global || self, factory(global.StagewiseToolbarReact = {}, global["@stagewise/toolbar"], global.react));
})(this, function(exports2, toolbar, react) {
  "use strict";
  function StagewiseToolbar({
    config,
    enabled = process.env.NODE_ENV === "development"
  }) {
    const isLoaded = react.useRef(false);
    react.useEffect(() => {
      if (isLoaded.current || !enabled) return;
      isLoaded.current = true;
      toolbar.initToolbar(config);
    }, [config, enabled]);
    return null;
  }
  exports2.StagewiseToolbar = StagewiseToolbar;
  Object.defineProperty(exports2, Symbol.toStringTag, { value: "Module" });
});
