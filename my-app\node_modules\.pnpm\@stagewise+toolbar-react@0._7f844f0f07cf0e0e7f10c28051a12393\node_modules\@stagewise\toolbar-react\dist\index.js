import { initToolbar } from "@stagewise/toolbar";
import { useRef, useEffect } from "react";
function StagewiseToolbar({
  config,
  enabled = process.env.NODE_ENV === "development"
}) {
  const isLoaded = useRef(false);
  useEffect(() => {
    if (isLoaded.current || !enabled) return;
    isLoaded.current = true;
    initToolbar(config);
  }, [config, enabled]);
  return null;
}
export {
  StagewiseToolbar
};
